#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码问题诊断和修复脚本
用于检测和修复Windows环境下的中文字符编码问题

使用方法:
1. 直接运行进行诊断: python fix_encoding.py
2. 应用修复: python fix_encoding.py --fix
3. 测试编码: python fix_encoding.py --test
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def check_current_encoding():
    """检查当前的编码设置"""
    print("=== 当前编码环境检查 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {sys.platform}")
    
    # 检查环境变量
    env_vars = [
        "PYTHONUTF8",
        "PYTHONIOENCODING", 
        "PYTHONLEGACYWINDOWSSTDIO",
        "LC_ALL",
        "LANG"
    ]
    
    print("\n环境变量:")
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"  {var}: {value}")
    
    # 检查默认编码
    print(f"\n默认编码:")
    print(f"  sys.getdefaultencoding(): {sys.getdefaultencoding()}")
    print(f"  sys.stdout.encoding: {getattr(sys.stdout, 'encoding', '未知')}")
    print(f"  sys.stderr.encoding: {getattr(sys.stderr, 'encoding', '未知')}")
    
    # 检查文件系统编码
    print(f"  文件系统编码: {sys.getfilesystemencoding()}")


def test_unicode_output():
    """测试Unicode字符输出"""
    print("\n=== Unicode输出测试 ===")
    
    test_strings = [
        "简单中文测试",
        "特殊字符: ①②③④⑤",
        "emoji测试: 🚀🔧⚠️",
        "混合内容: DJI Mini 3 Pro 无人机（95新）",
        "商品标题: mini3pro配件如图所示：机身（95新）+屏控+原装一长"
    ]
    
    for i, test_str in enumerate(test_strings, 1):
        try:
            print(f"测试 {i}: {test_str}")
            print(f"  ✅ 输出成功")
        except UnicodeEncodeError as e:
            print(f"  ❌ 编码错误: {e}")
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")


def apply_encoding_fix():
    """应用编码修复"""
    print("\n=== 应用编码修复 ===")
    
    if sys.platform == "win32":
        print("检测到Windows系统，应用编码修复...")
        
        # 设置环境变量
        fixes = {
            "PYTHONUTF8": "1",
            "PYTHONIOENCODING": "utf-8",
            "PYTHONLEGACYWINDOWSSTDIO": "0"
        }
        
        for var, value in fixes.items():
            os.environ[var] = value
            print(f"  设置 {var}={value}")
        
        # 重新配置stdout和stderr
        try:
            import codecs
            if hasattr(sys.stdout, 'detach'):
                sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
                sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
                print("  ✅ 重新配置标准输出编码成功")
            else:
                print("  ⚠️  无法重新配置标准输出（可能已经配置过）")
        except Exception as e:
            print(f"  ❌ 配置标准输出时出错: {e}")
    else:
        print("非Windows系统，通常不需要特殊编码设置")


def generate_batch_script():
    """生成Windows批处理脚本"""
    if sys.platform != "win32":
        print("非Windows系统，跳过批处理脚本生成")
        return
    
    batch_content = """@echo off
chcp 65001 >nul
set PYTHONUTF8=1
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=0

echo 已设置UTF-8编码环境变量
echo.
echo 现在可以运行Python脚本:
echo   python spider_v2.py
echo   python web_server.py
echo.
echo 或者启动一个新的命令行窗口:
cmd /k
"""
    
    script_path = Path("run_with_utf8.bat")
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print(f"\n✅ 已生成批处理脚本: {script_path}")
        print("使用方法: 双击运行 run_with_utf8.bat，然后在新窗口中运行Python脚本")
    except Exception as e:
        print(f"❌ 生成批处理脚本失败: {e}")


def check_project_files():
    """检查项目文件的编码问题"""
    print("\n=== 项目文件编码检查 ===")
    
    # 检查关键文件
    key_files = [
        "spider_v2.py",
        "web_server.py", 
        "src/utils.py",
        "src/ai_handler.py",
        ".env"
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"  ✅ {file_path}: UTF-8编码正常")
            except UnicodeDecodeError:
                print(f"  ❌ {file_path}: 可能存在编码问题")
            except Exception as e:
                print(f"  ⚠️  {file_path}: 检查时出错 - {e}")
        else:
            print(f"  ⚠️  {file_path}: 文件不存在")


def main():
    parser = argparse.ArgumentParser(description="编码问题诊断和修复工具")
    parser.add_argument("--fix", action="store_true", help="应用编码修复")
    parser.add_argument("--test", action="store_true", help="测试Unicode输出")
    parser.add_argument("--batch", action="store_true", help="生成批处理脚本")
    
    args = parser.parse_args()
    
    print("🔧 AI闲鱼监控 - 编码问题诊断和修复工具")
    print("=" * 50)
    
    # 总是执行基础检查
    check_current_encoding()
    check_project_files()
    
    if args.fix:
        apply_encoding_fix()
    
    if args.test or args.fix:
        test_unicode_output()
    
    if args.batch:
        generate_batch_script()
    
    if not any([args.fix, args.test, args.batch]):
        print("\n=== 建议的解决方案 ===")
        print("1. 运行 'python fix_encoding.py --fix --test' 应用修复并测试")
        print("2. 运行 'python fix_encoding.py --batch' 生成批处理脚本")
        print("3. 在PowerShell中设置环境变量:")
        print("   $env:PYTHONUTF8='1'")
        print("   $env:PYTHONIOENCODING='utf-8'")
        print("4. 或在CMD中设置:")
        print("   set PYTHONUTF8=1")
        print("   set PYTHONIOENCODING=utf-8")


if __name__ == "__main__":
    main()
